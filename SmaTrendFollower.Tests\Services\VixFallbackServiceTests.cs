using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using SmaTrendFollower.Services;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests.Services;

[TestClass]
[TestCategory(TestCategories.Unit)]
public class VixFallbackServiceTests
{
    private Mock<ILogger<VixFallbackService>> _mockLogger;
    private Mock<IAlpacaClientFactory> _mockAlpacaFactory;
    private Mock<IHttpClientFactory> _mockHttpClientFactory;
    private Mock<HttpClient> _mockHttpClient;
    private VixFallbackService _service;

    [TestInitialize]
    public void Setup()
    {
        _mockLogger = new Mock<ILogger<VixFallbackService>>();
        _mockAlpacaFactory = new Mock<IAlpacaClientFactory>();
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _mockHttpClient = new Mock<HttpClient>();

        _mockHttpClientFactory.Setup(x => x.CreateClient("vix-fallback"))
            .Returns(_mockHttpClient.Object);

        _service = new VixFallbackService(
            _mockLogger.Object,
            _mockAlpacaFactory.Object,
            _mockHttpClientFactory.Object);
    }

    [TestMethod]
    [TestTimeout(TestTimeouts.Unit)]
    public async Task GetVixFromWebAsync_ShouldReturnNull_WhenNoSourcesAvailable()
    {
        // Arrange
        _mockHttpClient.Setup(x => x.GetStringAsync(It.IsAny<string>()))
            .ThrowsAsync(new HttpRequestException("Network error"));

        // Act
        var result = await _service.GetVixFromWebAsync();

        // Assert
        result.Should().BeNull();
    }

    [TestMethod]
    [TestTimeout(TestTimeouts.Unit)]
    public async Task CalculateSyntheticVixAsync_ShouldReturnNull_WhenNoEtfDataAvailable()
    {
        // Arrange
        var mockDataClient = new Mock<IAlpacaDataClient>();
        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarsAsync(It.IsAny<LatestMarketDataRequest>()))
            .ReturnsAsync(new Dictionary<string, IBar>());

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert
        result.Should().BeNull();
    }

    [TestMethod]
    [TestTimeout(TestTimeouts.Unit)]
    public async Task CalculateSyntheticVixAsync_ShouldCalculateFromVxx_WhenVxxDataAvailable()
    {
        // Arrange
        var mockDataClient = new Mock<IAlpacaDataClient>();
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(25.0m); // VXX price

        var barData = new Dictionary<string, IBar>
        {
            ["VXX"] = mockBar.Object
        };

        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarsAsync(It.IsAny<LatestMarketDataRequest>()))
            .ReturnsAsync(barData);

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeGreaterThan(0);
        result.Should().BeLessOrEqualTo(80); // Sanity check for VIX range
        
        // VXX * 0.65 + 7 = 25 * 0.65 + 7 = 23.25
        result.Should().BeApproximately(23.25m, 0.1m);
    }

    [TestMethod]
    [TestTimeout(TestTimeouts.Unit)]
    public async Task CalculateSyntheticVixAsync_ShouldCalculateFromUvxy_WhenOnlyUvxyAvailable()
    {
        // Arrange
        var mockDataClient = new Mock<IAlpacaDataClient>();
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(10.0m); // UVXY price

        var barData = new Dictionary<string, IBar>
        {
            ["UVXY"] = mockBar.Object
        };

        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarsAsync(It.IsAny<LatestMarketDataRequest>()))
            .ReturnsAsync(barData);

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeGreaterThan(0);
        result.Should().BeLessOrEqualTo(80); // Sanity check for VIX range
        
        // UVXY * 1.8 + 10 = 10 * 1.8 + 10 = 28
        result.Should().BeApproximately(28.0m, 0.1m);
    }

    [TestMethod]
    [TestTimeout(TestTimeouts.Unit)]
    public async Task GetVixFromBraveSearchAsync_ShouldReturnNull_WhenNotImplemented()
    {
        // Act
        var result = await _service.GetVixFromBraveSearchAsync();

        // Assert
        result.Should().BeNull();
    }

    [TestMethod]
    [TestTimeout(TestTimeouts.Unit)]
    public async Task CalculateSyntheticVixAsync_ShouldClampValues_WhenCalculationOutOfRange()
    {
        // Arrange
        var mockDataClient = new Mock<IAlpacaDataClient>();
        var mockBar = new Mock<IBar>();
        mockBar.Setup(x => x.Close).Returns(200.0m); // Extremely high VXX price

        var barData = new Dictionary<string, IBar>
        {
            ["VXX"] = mockBar.Object
        };

        _mockAlpacaFactory.Setup(x => x.CreateDataClient())
            .Returns(mockDataClient.Object);

        mockDataClient.Setup(x => x.GetLatestBarsAsync(It.IsAny<LatestMarketDataRequest>()))
            .ReturnsAsync(barData);

        // Act
        var result = await _service.CalculateSyntheticVixAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeLessOrEqualTo(80m); // Should be clamped to max
        result.Should().BeGreaterOrEqualTo(10m); // Should be clamped to min
    }

    [TestCleanup]
    public void Cleanup()
    {
        _service?.Dispose();
    }
}
